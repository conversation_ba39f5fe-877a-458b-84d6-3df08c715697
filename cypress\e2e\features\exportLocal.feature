Feature: ExportLocal

  Background:
    Given The user is on the media list page

  # @e2e @exportLocal
  # Scenario: User exports a single file and all items
  #   When The user exports a file
  #   And The user exports all items

  # @e2e @exportLocal
  # Scenario: Verify the content of the Advanced Export pop-up
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   Then The "Advanced Export" pop-up is displayed
  #   And The "Fields" tab should be selected
  #   And The following export options should be visible:
  #     | Option Name     |
  #     | File Properties |
  #     | Transcription   |
  #     | Other Cognition |
  #   And The following buttons should be visible on the export pop-up:
  #     | Button Name   |
  #     | Cancel        |
  #     | Save Template |
  #     | Export        |

  # @e2e @exportLocal
  # Scenario: Verify Fields tab of Advanced Export pop-up
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   Then The "Advanced Export" pop-up is displayed
  #   And The "File ID" export option should be checked
  #   And The "File ID" export option should be disabled
  #   And The "Plain Text (TXT)" export option should be checked
  #   When The user checks the "Filename" export option
  #   Then The "Filename" export option should be checked
  #   When The user unchecks the "Plain Text (TXT)" export option
  #   Then The "Plain Text (TXT)" export option should not be checked

  # @e2e @exportLocal
  # Scenario: Verify user can edit name of each field
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   And The user clicks the edit icon for the "Plain Text (TXT)" field
  #   And The user renames the field "Plain Text (TXT)" to "My Custom TXT Name"
  #   And The user clicks the "OK" button in the dialog
  #   When The user clicks the edit icon for the "Plain Text (TXT)" field
  #   Then The input for "Plain Text (TXT)" should have the value "My Custom TXT Name"

  @e2e @exportLocal
  Scenario: Verify user can create your own export template
    When The user selects the file "e2e_audio.mp3"
    And The user clicks the "Export" toolbar button
    Then The "Advanced Export" pop-up is displayed
    When The user checks the "Filename" export option
    And The user clicks the "Save Template" button on the export pop-up
    Then The "Name Template" dialog should appear
    When The user names the template "My New Test Template"
    And The user clicks the "Save template" button in the new template dialog
    Then A success message "Template saved" is displayed

  # @e2e @exportLocal
  # Scenario: Verify saving multiple checked options to a new template
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   Then The "Advanced Export" pop-up is displayed
  #   When The user checks the following export options:
  #     | Option Name   |
  #     | Filename      |
  #     | Base Filename |
  #     | Tags          |
  #   And The user clicks the "Save Template" button on the export pop-up
  #   And The user names the template "My Multi-Option Template"
  #   And The user clicks the "Save template" button in the new template dialog
  #   And A success message "Template saved" is displayed
  #   When The user unchecks the following export options:
  #     | Option Name   |
  #     | Filename      |
  #     | Base Filename |
  #     | Tags          |
  #   And The user selects the export template "My Multi-Option Template"
  #   Then The following export options should be checked:
  #     | Option Name   |
  #     | Filename      |
  #     | Base Filename |
  #     | Tags          |

  # @e2e @exportLocal
  # Scenario: Verify user can export file using Export Templates
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   Then The "Advanced Export" pop-up is displayed
  #   When The user selects the export template "E2E - Test Template"
  #   And The user clicks the "Export" button on the export pop-up
  #   Then A success message "Your export job has been submitted" is displayed

  # @e2e @exportLocal
  # Scenario: Verify user can change Archive Name in Advanced Export
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   And The user navigates to the "Options" tab
  #   When The user clears the Archive Name field
  #   Then The Archive Name field should show the error message "invalid name. Minimum 3 characters required"
  #   And The "Export" button on the export pop-up should be disabled
  #   When The user sets the Archive Name to "My-Custom-Archive-Name"
  #   Then The Archive Name field should not show an error message
  #   And The "Export" button on the export pop-up should be enabled
  #   When The user clicks the "Export" button on the export pop-up
  #   Then A success message "Your export job has been submitted" is displayed

  # @e2e @exportLocal
  # Scenario: Verify user can set password for file before export
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   And The user navigates to the "Options" tab
  #   And The user clicks the "Set Password" button in option tab
  #   Then The "Set Password" dialog of option should appear
  #   When The user enters the password "MySecretPassword"
  #   And The user clicks the "Save" button in the password dialog
  #   Then The "Set Password" dialog should disappear
  #   When The user clicks the "Export" button on the export pop-up
  #   Then A success message "Your export job has been submitted" is displayed

  # @e2e @exportLocal
  # Scenario: Verify user can download exported file by 2 ways
  #   When The user selects the file "e2e_audio.mp3"
  #   And The user clicks the "Export" toolbar button
  #   And The user clicks the "Export" button on the export pop-up
  #   Then A success message "Your export job has been submitted" is displayed
  #   When The user opens the notifications panel
  #   And The user downloads the first completed export from the notifications panel
  #   And The user closes the notifications panel
  #   And The user navigates to the main application tab "EXPORTS("
  #   Then The user can download the first completed export from the exports table

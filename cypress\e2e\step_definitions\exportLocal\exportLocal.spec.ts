import {
  Before,
  DataTable,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
});

Given('The user is on the media list page', () => {
  mediaListPage.goToMediaListPage();
});

When('The user exports a file', () => {
  mediaListPage.exportFile();
});

When('The user exports all items', () => {
  mediaListPage.exportAllItems();
});

When('The user selects the file {string}', (fileName: string) => {
  cy.contains('[data-testid^="files-table-row-"]', fileName)
    .first()
    .find('input[type="checkbox"]')
    .check();
});

When('The user clicks the {string} toolbar button', (buttonName: string) => {
  const buttonSelectors: { [key: string]: string } = {
    tag: '[data-test="files-bulk-tag-icon-button"]',
    export: '[data-test="files-bulk-export-icon-button"]',
    'run process': '[data-test="reprocess-file-icon-button"]',
    move: '[data-test="files-bulk-move-icon-button"]',
    delete: '[data-test="files-bulk-delete-icon-button"]',
    'send to redact': '[data-test="files-send-to-redact-icon-button"]',
  };

  const selector = buttonSelectors[buttonName.toLowerCase()];
  if (!selector) {
    throw new Error(`Button "${buttonName}" is not defined`);
  }

  cy.get(selector).click();
});

Then('The "Advanced Export" pop-up is displayed', () => {
  cy.get('[data-test="panel-bulk-export"]')
    .should('be.visible')
    .within(() => {
      cy.get('h1').contains('advanced export', { matchCase: false });
    });
});

Then('The {string} tab should be selected', (tabName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .find('button[role="tab"]')
    .contains(tabName)
    .should('have.attr', 'aria-selected', 'true');
});

Then(
  'The following export options should be visible:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    cy.get('[data-test="panel-bulk-export"]').within(() => {
      options.forEach((optionName: string) => {
        cy.contains('p', optionName).should('be.visible');
      });
    });
  }
);

Then(
  'The following buttons should be visible on the export pop-up:',
  (dataTable: DataTable) => {
    const buttons = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    cy.get('[data-test="panel-bulk-export"]').within(() => {
      buttons.forEach((buttonName: string) => {
        cy.contains('button', buttonName).should('be.visible');
      });
    });
  }
);

Then('The {string} export option should be checked', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .should('be.checked');
});

Then('The {string} export option should be disabled', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .should('be.disabled');
});

When('The user checks the {string} export option', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .check({ force: true });
});

When('The user unchecks the {string} export option', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .uncheck({ force: true });
});

Then(
  'The {string} export option should not be checked',
  (optionName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('not.be.checked');
  }
);

When(
  'The user clicks the edit icon for the {string} field',
  (fieldName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .find(`label:has(p:contains("${fieldName}")) + [data-testid="EditIcon"]`)
      .click();
  }
);

When(
  'The user renames the field {string} to {string}',
  (originalName: string, newName: string) => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .clear();

      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .type(newName);
    });
  }
);

When(
  'The user clicks the {string} button in the dialog',
  (buttonName: string) => {
    cy.get('[role="dialog"]').contains('button', buttonName).click();
  }
);

Then(
  'The input for {string} should have the value {string}',
  (labelName: string, expectedValue: string) => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.get(`div:has(> label:contains("${labelName}"))`)
        .find('input[type="text"]')
        .should('have.value', expectedValue);
    });
  }
);

When(
  'The user clicks the {string} button on the export pop-up',
  (buttonName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  }
);

Then('The {string} dialog should appear', (name: string) => {
  cy.contains('[role="dialog"]', name).should('be.visible');
});

When('The user names the template {string}', (templateName: string) => {
  cy.contains('[role="dialog"]', 'Name Template').within(() => {
    cy.get('#export-template-name').clear();
    cy.get('#export-template-name').type(templateName);
  });
});

When(
  'The user clicks the {string} button in the new template dialog',
  (buttonName: string) => {
    cy.contains('[role="dialog"]', 'Name Template')
      .contains('button', buttonName)
      .click();
  }
);

Then('A success message {string} is displayed', (message: string) => {
  cy.get('[role="alert"]').should('be.visible').and('contain', message);
});

Given(
  'The user deletes the export template {string}',
  (templateName: string) => {
    cy.window().then((win) => {
      const apiRoot = Cypress.env('apiRoot');
      const token = Cypress.env('token');

      const exportTemplateDataRegistryId =
        win.config?.exportTemplateDataRegistryId;

      if (!exportTemplateDataRegistryId) {
        cy.log(
          'Export template data registry ID not found in config - skipping delete'
        );
        return cy.wrap(null);
      }

      const getSchemaQuery = `
        query getDataRegistry {
          dataRegistry(id: "${exportTemplateDataRegistryId}") {
            id
            name
            publishedSchema {
              id
            }
          }
        }
      `;

      cy.request({
        method: 'POST',
        url: `${apiRoot}/v3/graphql`,
        headers: { Authorization: `Bearer ${token}` },
        body: {
          query: getSchemaQuery,
          variables: {},
        },
        failOnStatusCode: false,
      }).then((schemaResponse) => {
        if (
          schemaResponse.status !== 200 ||
          !schemaResponse.body?.data?.dataRegistry?.publishedSchema?.id
        ) {
          cy.log('Could not retrieve schema ID - skipping template delete');
          return cy.wrap(null);
        }

        const schemaId =
          schemaResponse.body.data.dataRegistry.publishedSchema.id;

        const getTemplatesQuery = `
          query getSdos {
            structuredDataObjects(schemaId: "${schemaId}") {
              count
              records {
                id
                data
                createdDateTime
              }
            }
          }
        `;

        cy.request({
          method: 'POST',
          url: `${apiRoot}/v3/graphql`,
          headers: { Authorization: `Bearer ${token}` },
          body: {
            query: getTemplatesQuery,
            variables: {},
          },
          failOnStatusCode: false, 
        }).then((templatesResponse) => {
          if (
            templatesResponse.status !== 200 ||
            !templatesResponse.body?.data?.structuredDataObjects?.records
          ) {
            cy.log('Could not retrieve templates - skipping template delete');
            return cy.wrap(null);
          }

          const templates =
            templatesResponse.body.data.structuredDataObjects.records;

          const targetTemplate = templates.find(
            (template: any) => template.data?.name === templateName
          );

          if (!targetTemplate) {
            cy.log(
              `Template "${templateName}" not found - nothing to delete (this is expected for clean state)`
            );
            return cy.wrap(null);
          }

          const deleteQuery = `
            mutation deleteSdo {
              deleteStructuredData(input: {
                id: "${targetTemplate.id}"
                schemaId: "${schemaId}"
              }) {
                id
              }
            }
          `;

          cy.request({
            method: 'POST',
            url: `${apiRoot}/v3/graphql`,
            headers: { Authorization: `Bearer ${token}` },
            body: {
              query: deleteQuery,
              variables: {},
            },
            failOnStatusCode: false,
          }).then((deleteResponse) => {
            if (
              deleteResponse.status === 200 &&
              deleteResponse.body?.data?.deleteStructuredData?.id
            ) {
              cy.log(
                `Successfully deleted template "${templateName}" with ID: ${targetTemplate.id}`
              );
            } else {
              cy.log(
                `Could not delete template "${templateName}" - this may be expected if it doesn't exist`
              );
            }
            return cy.wrap(null);
          });

          return cy.wrap(null);
        });

        return cy.wrap(null);
      });

      return cy.wrap(null);
    });
  }
);

When(
  'The user checks the following export options:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .check({ force: true });
    });
  }
);

When(
  'The user unchecks the following export options:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .uncheck({ force: true });
    });
  }
);

When(
  'The user selects the export template {string}',
  (templateName: string) => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      cy.get('#export-template-selected').click();
    });
    cy.get('[role="option"]').contains(templateName).click();
  }
);

Then(
  'The following export options should be checked:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .should('be.checked');
    });
  }
);

When('The user navigates to the {string} tab', (tabName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .find('button[role="tab"]')
    .contains(tabName)
    .click();
});

When('The user clears the Archive Name field', () => {
  cy.get('[data-testid="export-file-name"]').find('input').clear();
});

Then(
  'The Archive Name field should show the error message {string}',
  (errorMessage: string) => {
    cy.get('#export-file-name-helper-text')
      .should('be.visible')
      .and('contain', errorMessage);
  }
);

Then('The "Export" button on the export pop-up should be disabled', () => {
  cy.get('[data-test="export-select-button"]').should('be.disabled');
});

When('The user sets the Archive Name to {string}', (archiveName: string) => {
  cy.get('[data-testid="export-file-name"]').find('input').type(archiveName);
});

Then('The Archive Name field should not show an error message', () => {
  cy.get('#export-file-name-helper-text').should('not.have.class', 'Mui-error');
});

Then('The "Export" button on the export pop-up should be enabled', () => {
  cy.get('[data-test="export-select-button"]').should('be.enabled');
});

When(
  'The user clicks the {string} button in option tab',
  (buttonName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  }
);

Then('The "Set Password" dialog of option should appear', () => {
  cy.get('div:has(#encryption-password)').should('be.visible');
});

When('The user enters the password {string}', (password: string) => {
  cy.get('#encryption-password').type(password);
});

When(
  'The user clicks the {string} button in the password dialog',
  (buttonName: string) => {
    cy.get('div:has(#encryption-password)')
      .contains('button', buttonName)
      .click();
  }
);

Then('The "Set Password" dialog should disappear', () => {
  cy.get('#encryption-password').should('not.exist');
});

When('The user opens the notifications panel', () => {
  cy.get('#notification-image').click();
});

When(
  'The user downloads the first completed export from the notifications panel',
  () => {
    cy.contains('li', 'complete', { timeout: 120000 })
      .first()
      .find('button:has([data-testid="SaveAltIcon"])')
      .click();
  }
);

When('The user closes the notifications panel', () => {
  cy.get('body').click(0, 0);
});

When(
  'The user navigates to the main application tab {string}',
  (tabName: string) => {
    cy.get(`[data-test="history-tab-button"]`).contains(tabName).click();
  }
);

Then(
  'The user can download the first completed export from the exports table',
  () => {
    cy.get('tbody tr:has(span:contains("complete"))', { timeout: 120000 })
      .first()
      .find('button:has([data-testid="SaveAltIcon"])')
      .should('be.enabled');
  }
);

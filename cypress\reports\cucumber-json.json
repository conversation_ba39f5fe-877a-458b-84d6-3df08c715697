[{"description": "", "elements": [{"description": "", "id": "exportlocal;verify-user-can-create-your-own-export-template", "keyword": "<PERSON><PERSON><PERSON>", "line": 52, "name": "Verify user can create your own export template", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 33515000000}}, {"arguments": [], "keyword": "Given ", "line": 4, "name": "The user is on the media list page", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:14"}, "result": {"status": "passed", "duration": 6699000000}}, {"arguments": [], "keyword": "Given ", "line": 53, "name": "The user deletes the export template \"My New Test Template\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:214"}, "result": {"status": "passed", "duration": 771000000}}, {"arguments": [], "keyword": "When ", "line": 54, "name": "The user selects the file \"e2e_audio.mp3\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:26"}, "result": {"status": "passed", "duration": 707000000}}, {"arguments": [], "keyword": "And ", "line": 55, "name": "The user clicks the \"Export\" toolbar button", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:33"}, "result": {"status": "passed", "duration": 897000000}}, {"arguments": [], "keyword": "Then ", "line": 56, "name": "The \"Advanced Export\" pop-up is displayed", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:51"}, "result": {"status": "passed", "duration": 151000000}}, {"arguments": [], "keyword": "When ", "line": 57, "name": "The user checks the \"Filename\" export option", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:112"}, "result": {"status": "passed", "duration": 323000000}}, {"arguments": [], "keyword": "And ", "line": 58, "name": "The user clicks the \"Save Template\" button on the export pop-up", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:181"}, "result": {"status": "passed", "duration": 417000000}}, {"arguments": [], "keyword": "Then ", "line": 59, "name": "The \"Name Template\" dialog should appear", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:189"}, "result": {"status": "passed", "duration": 214000000}}, {"arguments": [], "keyword": "When ", "line": 60, "name": "The user names the template \"My New Test Template\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:193"}, "result": {"status": "passed", "duration": 1740000000}}, {"arguments": [], "keyword": "And ", "line": 61, "name": "The user clicks the \"Save template\" button in the new template dialog", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:201"}, "result": {"status": "passed", "duration": 767000000}}, {"arguments": [], "keyword": "Then ", "line": 62, "name": "A success message \"Template saved\" is displayed", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:209"}, "result": {"status": "passed", "duration": 601000000}}], "tags": [{"name": "@e2e", "line": 51}, {"name": "@exportLocal", "line": 51}], "type": "scenario"}], "id": "exportlocal", "line": 1, "keyword": "Feature", "name": "ExportLocal", "tags": [], "uri": "cypress\\e2e\\features\\exportLocal.feature"}]
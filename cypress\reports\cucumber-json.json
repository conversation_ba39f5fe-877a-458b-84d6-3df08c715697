[{"description": "", "elements": [{"description": "", "id": "exportlocal;verify-user-can-download-exported-file-by-2-ways", "keyword": "<PERSON><PERSON><PERSON>", "line": 126, "name": "Verify user can download exported file by 2 ways", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 21386000000}}, {"arguments": [], "keyword": "Given ", "line": 4, "name": "The user is on the media list page", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:14"}, "result": {"status": "passed", "duration": 6204000000}}, {"arguments": [], "keyword": "When ", "line": 127, "name": "The user selects the file \"e2e_audio.mp3\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:26"}, "result": {"status": "passed", "duration": 839000000}}, {"arguments": [], "keyword": "And ", "line": 128, "name": "The user clicks the \"Export\" toolbar button", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:33"}, "result": {"status": "passed", "duration": 561000000}}, {"arguments": [], "keyword": "And ", "line": 129, "name": "The user clicks the \"Export\" button on the export pop-up", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:181"}, "result": {"status": "passed", "duration": 332000000}}, {"arguments": [], "keyword": "Then ", "line": 130, "name": "A success message \"Your export job has been submitted\" is displayed", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:209"}, "result": {"status": "passed", "duration": 2288000000}}, {"arguments": [], "keyword": "When ", "line": 131, "name": "The user opens the notifications panel", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:337"}, "result": {"status": "passed", "duration": 460000000}}, {"arguments": [], "keyword": "And ", "line": 132, "name": "The user downloads the first completed export from the notifications panel", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:342"}, "result": {"status": "passed", "duration": 684000000}}, {"arguments": [], "keyword": "And ", "line": 133, "name": "The user closes the notifications panel", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:351"}, "result": {"status": "passed", "duration": 154000000}}, {"arguments": [], "keyword": "And ", "line": 134, "name": "The user navigates to the main application tab \"EXPORTS(\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:356"}, "result": {"status": "passed", "duration": 416000000}}, {"arguments": [], "keyword": "Then ", "line": 135, "name": "The user can download the first completed export from the exports table", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:363"}, "result": {"status": "passed", "duration": 88000000}}], "tags": [{"name": "@e2e", "line": 125}, {"name": "@exportLocal", "line": 125}], "type": "scenario"}], "id": "exportlocal", "line": 1, "keyword": "Feature", "name": "ExportLocal", "tags": [], "uri": "cypress\\e2e\\features\\exportLocal.feature"}]